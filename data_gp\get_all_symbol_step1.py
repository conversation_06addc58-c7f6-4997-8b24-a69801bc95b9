import akshare as ak


def get_all_stock_symbol():
    stock_info_sh_name_code_df = ak.stock_info_sh_name_code(symbol="主板A股")
    stock_info_sh_name_code_df.to_csv("沪市主板A股.csv", index=False, encoding="utf_8_sig")

    stock_info_sh_name_code_df = ak.stock_info_sh_name_code(symbol="科创板")
    stock_info_sh_name_code_df.to_csv("沪市科创板A股.csv", index=False, encoding="utf_8_sig")

    stock_info_sz_name_code_df = ak.stock_info_sz_name_code(symbol="A股列表")
    stock_info_sz_name_code_df.to_csv("深市主板A股.csv", index=False, encoding="utf_8_sig")


if __name__ == "__main__":
    get_all_stock_symbol()
