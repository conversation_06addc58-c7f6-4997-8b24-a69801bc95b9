# 沪深上市公司数据导入说明

## 文件说明

### 数据模型 (models/Stock.js)
- 定义了沪深上市公司的MongoDB数据模型
- 包含字段：板块、证券代码、证券简称、行业、上市时间等
- 提供了便捷的查询方法和索引优化

### 导入脚本 (scripts/importStocks.js)
- 从CSV文件读取股票数据并导入到MongoDB
- 支持数据清理和格式化
- 提供详细的进度显示和错误处理
- 导入完成后显示统计信息

## 使用步骤

### 1. 安装依赖
```powershell
cd server
npm install
```

### 2. 配置环境变量
确保 `.env` 文件中的 `MONGODB_URI` 配置正确：
```
MONGODB_URI=mongodb://localhost:27017/redSun
```

### 3. 运行导入脚本
```powershell
# 方式1：使用npm脚本
npm run import-stocks

# 方式2：直接运行
node scripts/importStocks.js
```

## 数据模型字段说明

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| boardType | String | 板块类型 | "主板", "科创板" |
| stockCode | String | 证券代码 | "000001" |
| stockName | String | 证券简称 | "平安银行" |
| industry | String | 所属行业 | "银行" |
| listingDate | Date | 上市时间(Date对象) | Date对象 |
| listingDateString | String | 上市时间(原始字符串) | "19910403" |
| isActive | Boolean | 是否有效 | true |
| createdAt | Date | 创建时间 | 自动生成 |
| updatedAt | Date | 更新时间 | 自动生成 |

## 模型方法

### 静态方法
- `Stock.findByCode(code)` - 根据股票代码查找
- `Stock.findByIndustry(industry)` - 根据行业查找
- `Stock.findByBoard(boardType)` - 根据板块查找

### 实例方法
- `stock.toDisplayString()` - 格式化显示股票信息

### 虚拟字段
- `listingYear` - 获取上市年份

## 使用示例

```javascript
const Stock = require('./models/Stock');

// 查找特定股票
const stock = await Stock.findByCode('000001');

// 查找银行业股票
const bankStocks = await Stock.findByIndustry('银行');

// 查找主板股票
const mainBoardStocks = await Stock.findByBoard('主板');

// 按上市时间排序
const recentStocks = await Stock.find()
  .sort({ listingDate: -1 })
  .limit(10);
```

## 注意事项

1. 导入前会清空现有的股票数据
2. 支持批量导入，默认每批500条记录
3. 遇到重复数据会跳过并继续导入
4. 导入完成后会显示详细的统计信息
5. 确保MongoDB服务正在运行

## 故障排除

### 常见错误
1. **MongoDB连接失败**：检查数据库服务是否启动，端口是否正确
2. **CSV文件读取失败**：检查文件路径和编码格式
3. **日期格式错误**：确保CSV中的日期格式为YYYYMMDD

### 日志说明
- ✅ 成功操作
- ❌ 错误信息
- ⚠️ 警告信息
- 🔄 进行中的操作
- 📊 统计信息
