#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
股票信息批量获取脚本

脚本作用：
1. 读取包含股票基本信息的CSV文件（包含板块、证券代码、证券简称等字段）
2. 通过akshare库的stock_individual_info_em接口批量获取每只股票的详细信息
3. 从API返回的数据中提取"行业"和"上市时间"两个关键字段
4. 将获取到的行业和上市时间信息与原始CSV数据合并
5. 保存到新的CSV文件中，保持股票代码格式完整性（如000001不会变成1）

主要功能特点：
- 自动处理股票代码格式，确保6位数字格式
- 包含完善的错误处理机制，单个股票获取失败不影响整体处理
- 设置合理的API请求间隔，避免因频繁调用被封禁
- 实时显示处理进度和详细日志信息
- 支持大批量股票数据处理

输入文件格式要求：
CSV文件需包含以下列：板块,证券代码,证券简称

输出文件格式：
在原有字段基础上增加：行业,上市时间

使用前请确保已安装依赖：pip install akshare pandas

作者：AI助手
版本：1.0
创建时间：2024
"""

import pandas as pd
import akshare as ak
import time
import logging
from datetime import datetime

# 设置日志格式
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# API请求间隔设置（秒）
API_REQUEST_INTERVAL = 2  # 每次API调用间隔2秒，避免被封禁

def get_stock_info(stock_code):
    """
    获取股票的行业和上市时间信息
    
    参数:
        stock_code (str): 股票代码
    
    返回:
        tuple: (行业, 上市时间) 如果获取失败则返回 (None, None)
    """
    try:
        # 确保股票代码格式正确（6位数字，不足6位前面补0）
        stock_code = str(stock_code).zfill(6)
        
        logging.info(f"正在调用API获取股票 {stock_code} 的信息...")
        
        # 调用API获取股票信息
        stock_info = ak.stock_individual_info_em(symbol=stock_code)
        
        # 初始化返回值
        industry = None
        listing_date = None
        
        # 遍历返回的数据，查找行业和上市时间
        for index, row in stock_info.iterrows():
            if row['item'] == '行业':
                industry = row['value']
                logging.debug(f"股票 {stock_code} 行业: {industry}")
            elif row['item'] == '上市时间':
                listing_date = row['value']
                logging.debug(f"股票 {stock_code} 上市时间: {listing_date}")
        
        logging.info(f"股票 {stock_code} 信息获取成功")
        return industry, listing_date
        
    except Exception as e:
        logging.error(f"获取股票 {stock_code} 信息失败: {str(e)}")
        return None, None

def process_stock_csv(input_file, output_file):
    """
    处理CSV文件，获取股票信息并保存到新文件
    
    参数:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
    
    返回:
        pandas.DataFrame: 处理后的数据框，如果失败返回None
    """
    try:
        # 读取原始CSV文件，确保证券代码作为字符串读取
        logging.info(f"正在读取输入文件: {input_file}")
        df = pd.read_csv(input_file, dtype={'证券代码': str})
        
        total_stocks = len(df)
        logging.info(f"共需要处理 {total_stocks} 只股票")
        
        # 添加新列
        df['行业'] = ''
        df['上市时间'] = ''
        
        # 记录处理开始时间
        start_time = datetime.now()
        success_count = 0
        fail_count = 0
        
        # 遍历每一行数据
        for index, row in df.iterrows():
            stock_code = str(row['证券代码']).zfill(6)  # 确保股票代码格式正确
            stock_name = row['证券简称']
            
            logging.info(f"正在处理第 {index + 1}/{total_stocks} 只股票: {stock_code} - {stock_name}")
            
            # 获取股票信息
            industry, listing_date = get_stock_info(stock_code)
            
            # 更新DataFrame
            if industry is not None and listing_date is not None:
                df.at[index, '行业'] = industry
                df.at[index, '上市时间'] = listing_date
                success_count += 1
                logging.info(f"股票 {stock_code} 处理成功")
            else:
                df.at[index, '行业'] = '未获取到'
                df.at[index, '上市时间'] = '未获取到'
                fail_count += 1
                logging.warning(f"股票 {stock_code} 处理失败")
            
            # 重要：API请求间隔，避免被封禁
            if index < total_stocks - 1:  # 最后一个不需要等待
                logging.info(f"等待 {API_REQUEST_INTERVAL} 秒后继续下一个请求...")
                time.sleep(API_REQUEST_INTERVAL)
            
            # 每处理10只股票显示一次进度统计
            if (index + 1) % 10 == 0:
                elapsed_time = datetime.now() - start_time
                avg_time_per_stock = elapsed_time.total_seconds() / (index + 1)
                remaining_stocks = total_stocks - (index + 1)
                estimated_remaining_time = remaining_stocks * avg_time_per_stock
                
                logging.info(f"进度统计: 已处理 {index + 1}/{total_stocks} 只股票")
                logging.info(f"成功: {success_count}, 失败: {fail_count}")
                logging.info(f"预计剩余时间: {estimated_remaining_time/60:.1f} 分钟")
        
        # 保存到新的CSV文件
        logging.info(f"正在保存结果到文件: {output_file}")
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 处理完成统计
        end_time = datetime.now()
        total_time = end_time - start_time
        
        logging.info("="*50)
        logging.info("处理完成统计:")
        logging.info(f"总处理股票数: {total_stocks}")
        logging.info(f"成功获取信息: {success_count}")
        logging.info(f"获取失败: {fail_count}")
        logging.info(f"成功率: {success_count/total_stocks*100:.1f}%")
        logging.info(f"总耗时: {total_time}")
        logging.info(f"结果已保存到: {output_file}")
        logging.info("="*50)
        
        return df
        
    except Exception as e:
        logging.error(f"处理文件时发生错误: {str(e)}")
        return None

def main():
    """
    主函数 - 程序入口点
    """
    print("="*60)
    print("股票信息批量获取脚本")
    print("="*60)
    print("功能: 批量获取股票的行业和上市时间信息")
    print(f"API请求间隔: {API_REQUEST_INTERVAL} 秒")
    print("="*60)
    
    # 输入和输出文件路径
    input_file = "沪深上市公司列表.csv"  # 您的原始CSV文件名
    output_file = "沪深上市公司列表_企业信息.csv"  # 输出文件名
    
    # 检查输入文件是否存在
    try:
        pd.read_csv(input_file, nrows=1)
        logging.info(f"输入文件 {input_file} 检查通过")
    except FileNotFoundError:
        logging.error(f"输入文件 {input_file} 不存在，请检查文件路径")
        return
    except Exception as e:
        logging.error(f"读取输入文件时发生错误: {str(e)}")
        return
    
    # 处理CSV文件
    result_df = process_stock_csv(input_file, output_file)
    
    if result_df is not None:
        print(f"\n处理完成！共处理了 {len(result_df)} 只股票")
        print(f"结果已保存到: {output_file}")
        print(f"详细日志已保存到: stock_processing.log")
        
        # 显示前几行结果作为预览
        print("\n结果预览:")
        print(result_df.head(3))
        
        # 显示统计信息
        success_count = len(result_df[result_df['行业'] != '未获取到'])
        fail_count = len(result_df[result_df['行业'] == '未获取到'])
        print(f"\n最终统计: 成功 {success_count} 只, 失败 {fail_count} 只")
        
    else:
        print("处理失败，请检查日志文件中的错误信息")

if __name__ == "__main__":
    main()
