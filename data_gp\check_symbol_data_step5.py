import pandas as pd
import os

def analyze_and_update_stock_data():
    """
    分析并更新股票数据的脚本
    """
    
    # 读取CSV文件
    try:
        listed_companies = pd.read_csv('沪深上市公司列表.csv')
        daily_stock_data = pd.read_csv('当日股票数据.csv')
        print("成功读取CSV文件")
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 确保证券代码列为字符串类型（避免前导零丢失）
    listed_companies['证券代码'] = listed_companies['证券代码'].astype(str).str.zfill(6)
    daily_stock_data['证券代码'] = daily_stock_data['证券代码'].astype(str).str.zfill(6)
    
    # 获取证券代码集合
    listed_codes = set(listed_companies['证券代码'])
    daily_codes = set(daily_stock_data['证券代码'])
    
    print("="*60)
    print("数据分析报告")
    print("="*60)
    
    # 1. 检查沪深上市公司列表中的代码是否都在当日股票数据中
    print("\n1. 沪深上市公司列表中但不在当日股票数据中的证券代码:")
    missing_in_daily = listed_codes - daily_codes
    if missing_in_daily:
        print(f"   共 {len(missing_in_daily)} 个:")
        for code in sorted(missing_in_daily):
            company_name = listed_companies[listed_companies['证券代码'] == code]['证券简称'].iloc[0]
            print(f"   {code} - {company_name}")
    else:
        print("   无缺失")
    
    # 2. 检查当日股票数据中的代码是否都在沪深上市公司列表中
    print("\n2. 当日股票数据中但不在沪深上市公司列表中的证券代码:")
    missing_in_listed = daily_codes - listed_codes
    if missing_in_listed:
        print(f"   共 {len(missing_in_listed)} 个:")
        for code in sorted(missing_in_listed):
            company_name = daily_stock_data[daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
            print(f"   {code} - {company_name}")
    else:
        print("   无缺失")
    
    # 3. 检查两个文件中都有的证券代码对应的证券简称是否一致
    print("\n3. 证券简称不一致的记录:")
    common_codes = listed_codes & daily_codes
    name_mismatch = []
    
    for code in common_codes:
        listed_name = listed_companies[listed_companies['证券代码'] == code]['证券简称'].iloc[0]
        daily_name = daily_stock_data[daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
        
        if listed_name != daily_name:
            name_mismatch.append({
                '证券代码': code,
                '上市公司列表中的简称': listed_name,
                '当日数据中的简称': daily_name
            })
    
    if name_mismatch:
        print(f"   共 {len(name_mismatch)} 个不一致:")
        for item in name_mismatch:
            print(f"   {item['证券代码']}: 上市公司列表='{item['上市公司列表中的简称']}', 当日数据='{item['当日数据中的简称']}'")
    else:
        print("   所有证券简称都一致")
    
    # 4. 更新当日股票数据中的板块信息
    print("\n4. 更新板块信息:")
    
    # 创建证券代码到板块的映射字典
    code_to_sector = dict(zip(listed_companies['证券代码'], listed_companies['板块']))
    
    # 备份原始数据
    daily_stock_data_updated = daily_stock_data.copy()
    
    # 统计更新情况
    updated_count = 0
    unchanged_count = 0
    
    for index, row in daily_stock_data_updated.iterrows():
        code = row['证券代码']
        if code in code_to_sector:
            old_sector = row['板块']
            new_sector = code_to_sector[code]
            
            if old_sector != new_sector:
                daily_stock_data_updated.at[index, '板块'] = new_sector
                updated_count += 1
            else:
                unchanged_count += 1
    
    print(f"   更新了 {updated_count} 条记录的板块信息")
    print(f"   {unchanged_count} 条记录的板块信息无需更新")
    print(f"   {len(missing_in_listed)} 条记录因不在上市公司列表中而无法更新板块信息")
    
    # 保存更新后的完整文件
    output_filename = '当日股票数据_已更新板块.csv'
    daily_stock_data_updated.to_csv(output_filename, index=False, encoding='utf-8-sig')
    print(f"\n已将更新后的数据保存为: {output_filename}")
    
    # 5. 新功能：生成清洗后的数据文件（仅包含两者共有的数据，按上市公司列表排序）
    print("\n5. 生成清洗后的数据文件:")
    
    # 筛选出两者共有的数据
    common_data = daily_stock_data_updated[daily_stock_data_updated['证券代码'].isin(common_codes)].copy()
    
    # 为了按照沪深上市公司列表的顺序排序，我们需要创建一个排序键
    # 首先为上市公司列表添加排序索引
    listed_companies_with_order = listed_companies.reset_index()
    listed_companies_with_order = listed_companies_with_order.rename(columns={'index': 'sort_order'})
    
    # 创建证券代码到排序顺序的映射
    code_to_order = dict(zip(listed_companies_with_order['证券代码'], 
                            listed_companies_with_order['sort_order']))
    
    # 为共同数据添加排序键
    common_data['sort_order'] = common_data['证券代码'].map(code_to_order)
    
    # 按照上市公司列表的原始顺序排序
    common_data_sorted = common_data.sort_values('sort_order')
    
    # 删除临时的排序列
    common_data_sorted = common_data_sorted.drop('sort_order', axis=1)
    
    # 保存清洗后的文件
    cleaned_filename = '当日股票数据_清洗后.csv'
    common_data_sorted.to_csv(cleaned_filename, index=False, encoding='utf-8-sig')
    
    print(f"   清洗后的数据包含 {len(common_data_sorted)} 条记录")
    print(f"   已保存为: {cleaned_filename}")
    print(f"   排序顺序与沪深上市公司列表.csv保持一致")
    
    # 生成详细报告
    print("\n" + "="*60)
    print("统计摘要")
    print("="*60)
    print(f"沪深上市公司列表总数: {len(listed_companies)}")
    print(f"当日股票数据总数: {len(daily_stock_data)}")
    print(f"两个文件共同包含的证券代码数: {len(common_codes)}")
    print(f"仅在上市公司列表中的代码数: {len(missing_in_daily)}")
    print(f"仅在当日数据中的代码数: {len(missing_in_listed)}")
    print(f"证券简称不一致的数量: {len(name_mismatch)}")
    print(f"板块信息已更新的记录数: {updated_count}")
    print(f"清洗后数据记录数: {len(common_data_sorted)}")
    
    # 显示清洗过程的统计信息
    print(f"\n清洗过程统计:")
    print(f"原始当日股票数据: {len(daily_stock_data)} 条")
    print(f"移除不在上市公司列表中的数据: {len(missing_in_listed)} 条")
    print(f"最终清洗后数据: {len(common_data_sorted)} 条")
    print(f"数据保留率: {len(common_data_sorted)/len(daily_stock_data)*100:.2f}%")
    
    # 可选：生成详细的差异报告文件
    if missing_in_daily or missing_in_listed or name_mismatch:
        generate_detailed_report(missing_in_daily, missing_in_listed, name_mismatch, 
                               listed_companies, daily_stock_data)

def generate_detailed_report(missing_in_daily, missing_in_listed, name_mismatch, 
                           listed_companies, daily_stock_data):
    """
    生成详细的差异报告文件
    """
    with open('股票数据差异报告.txt', 'w', encoding='utf-8') as f:
        f.write("股票数据差异详细报告\n")
        f.write("="*60 + "\n\n")
        
        f.write("1. 仅在沪深上市公司列表中存在的证券代码:\n")
        f.write("-"*40 + "\n")
        if missing_in_daily:
            for code in sorted(missing_in_daily):
                company_name = listed_companies[listed_companies['证券代码'] == code]['证券简称'].iloc[0]
                sector = listed_companies[listed_companies['证券代码'] == code]['板块'].iloc[0]
                f.write(f"{code}\t{company_name}\t{sector}\n")
        else:
            f.write("无\n")
        
        f.write("\n2. 仅在当日股票数据中存在的证券代码:\n")
        f.write("-"*40 + "\n")
        if missing_in_listed:
            for code in sorted(missing_in_listed):
                company_name = daily_stock_data[daily_stock_data['证券代码'] == code]['证券简称'].iloc[0]
                sector = daily_stock_data[daily_stock_data['证券代码'] == code]['板块'].iloc[0]
                f.write(f"{code}\t{company_name}\t{sector}\n")
        else:
            f.write("无\n")
        
        f.write("\n3. 证券简称不一致的记录:\n")
        f.write("-"*40 + "\n")
        if name_mismatch:
            for item in name_mismatch:
                f.write(f"{item['证券代码']}\t上市公司列表: {item['上市公司列表中的简称']}\t当日数据: {item['当日数据中的简称']}\n")
        else:
            f.write("无\n")
    
    print("详细差异报告已保存为: 股票数据差异报告.txt")

if __name__ == "__main__":
    analyze_and_update_stock_data()
