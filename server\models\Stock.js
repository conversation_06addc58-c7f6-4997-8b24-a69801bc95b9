const mongoose = require('mongoose');

// 沪深上市公司数据模型
const stockSchema = new mongoose.Schema({
  // 板块（主板、科创板等）
  boardType: {
    type: String,
    required: true,
    trim: true
  },
  
  // 证券代码
  stockCode: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    index: true
  },
  
  // 证券简称
  stockName: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 行业
  industry: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  
  // 上市时间
  listingDate: {
    type: Date,
    required: true,
    index: true
  },
  
  // 上市时间字符串格式（原始格式）
  listingDateString: {
    type: String,
    required: true
  },
  
  // 是否有效（用于标记退市等状态）
  isActive: {
    type: Boolean,
    default: true
  },
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  // 自动添加 createdAt 和 updatedAt
  timestamps: true,
  
  // 设置集合名称
  collection: 'stocks'
});

// 添加索引
stockSchema.index({ boardType: 1, industry: 1 });
stockSchema.index({ listingDate: 1 });
stockSchema.index({ stockName: 'text', stockCode: 'text' }); // 全文搜索索引

// 静态方法：根据股票代码查找
stockSchema.statics.findByCode = function(code) {
  return this.findOne({ stockCode: code });
};

// 静态方法：根据行业查找
stockSchema.statics.findByIndustry = function(industry) {
  return this.find({ industry: industry });
};

// 静态方法：根据板块查找
stockSchema.statics.findByBoard = function(boardType) {
  return this.find({ boardType: boardType });
};

// 实例方法：格式化显示
stockSchema.methods.toDisplayString = function() {
  return `${this.stockCode} - ${this.stockName} (${this.industry})`;
};

// 中间件：更新时自动设置 updatedAt
stockSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 虚拟字段：获取上市年份
stockSchema.virtual('listingYear').get(function() {
  return this.listingDate.getFullYear();
});

// 确保虚拟字段包含在JSON输出中
stockSchema.set('toJSON', { virtuals: true });
stockSchema.set('toObject', { virtuals: true });

const Stock = mongoose.model('Stock', stockSchema);

module.exports = Stock;
