"""
脚本功能说明：
本脚本用于批量获取沪深上市公司的年度财务关键指标数据。

主要功能：
1. 读取"沪深上市公司列表.csv"文件中的所有证券代码
2. 遍历每个证券代码，调用akshare的stock_financial_abstract_ths接口获取年度财务数据
3. 为每条数据添加"证券代码"和"获取方式"字段
4. 分批次保存数据，避免因意外中断导致数据丢失
5. 支持断点续传功能，可以从上次中断的地方继续
6. 输出文件命名为"企业关键指标年度.csv"

注意事项：
- 需要安装akshare库：pip install akshare
- 批次大小可以根据需要调整（默认每10个公司保存一次）
- 如果某个证券代码获取失败，会跳过并继续处理下一个
- 支持断点续传，重新运行时会跳过已处理的公司
- 已修复证券代码前导零丢失的问题
"""

import pandas as pd
import akshare as ak
import time
import os
from tqdm import tqdm

def get_processed_codes(output_file):
    """
    获取已经处理过的证券代码列表，用于断点续传
    """
    if os.path.exists(output_file):
        try:
            # 同样需要指定证券代码为字符串类型
            existing_data = pd.read_csv(output_file, encoding='utf-8-sig', dtype={'证券代码': str})
            processed_codes = existing_data['证券代码'].unique().tolist()
            return processed_codes
        except:
            return []
    return []

def save_batch_data(batch_data, output_file, is_first_batch=False):
    """
    保存批次数据到CSV文件
    
    Args:
        batch_data: 要保存的数据
        output_file: 输出文件名
        is_first_batch: 是否为第一个批次（决定是否写入表头）
    """
    try:
        if is_first_batch or not os.path.exists(output_file):
            # 第一次写入或文件不存在时，写入表头
            batch_data.to_csv(output_file, index=False, encoding='utf-8-sig', mode='w')
        else:
            # 追加模式，不写入表头
            batch_data.to_csv(output_file, index=False, encoding='utf-8-sig', mode='a', header=False)
        return True
    except Exception as e:
        print(f"保存批次数据失败：{e}")
        return False

def format_stock_code(code):
    """
    格式化证券代码，确保是6位字符串格式
    
    Args:
        code: 原始证券代码
    
    Returns:
        str: 格式化后的6位证券代码
    """
    # 转换为字符串并去除空格
    code_str = str(code).strip()
    
    # 如果长度不足6位，前面补0
    if len(code_str) < 6:
        code_str = code_str.zfill(6)
    
    return code_str

def get_stock_financial_data(batch_size=10):
    """
    主函数：批量获取上市公司财务数据
    
    Args:
        batch_size: 批次大小，每处理多少个公司后保存一次
    """
    
    output_file = "企业关键指标年度.csv"
    
    # 1. 读取上市公司列表 - 关键修改：指定证券代码为字符串类型
    try:
        # 使用dtype参数强制指定证券代码列为字符串类型，防止前导零丢失
        company_list = pd.read_csv("沪深上市公司列表.csv", 
                                 encoding='utf-8', 
                                 dtype={'证券代码': str})
        
        # 格式化证券代码，确保都是6位
        company_list['证券代码'] = company_list['证券代码'].apply(format_stock_code)
        
        print(f"成功读取上市公司列表，共{len(company_list)}家公司")
        
        # 显示前几个证券代码以验证格式
        print("前5个证券代码示例：")
        for i, row in company_list.head().iterrows():
            print(f"  {row['证券代码']} - {row['证券简称']}")
            
    except Exception as e:
        print(f"读取上市公司列表失败：{e}")
        return
    
    # 2. 获取已处理的证券代码（用于断点续传）
    processed_codes = get_processed_codes(output_file)
    if processed_codes:
        print(f"检测到已处理的数据，共{len(processed_codes)}个证券代码")
        # 过滤掉已处理的公司
        company_list = company_list[~company_list['证券代码'].isin(processed_codes)]
        print(f"剩余待处理公司：{len(company_list)}家")
    
    if len(company_list) == 0:
        print("所有公司数据已获取完成！")
        return
    
    # 3. 初始化变量
    batch_data = pd.DataFrame()
    failed_codes = []
    processed_count = 0
    success_count = 0
    is_first_batch = len(processed_codes) == 0  # 判断是否为第一个批次
    
    # 4. 遍历每个证券代码获取数据
    for index, row in tqdm(company_list.iterrows(), total=len(company_list), desc="获取财务数据"):
        stock_code = row['证券代码']
        stock_name = row['证券简称']
        
        # 验证证券代码格式
        if len(stock_code) != 6 or not stock_code.isdigit():
            print(f"警告：证券代码格式异常 {stock_code}({stock_name})，跳过")
            failed_codes.append(stock_code)
            processed_count += 1
            continue
        
        try:
            # 调用akshare接口获取年度财务数据
            # 确保传入的是字符串格式的6位代码
            financial_data = ak.stock_financial_abstract_ths(symbol=stock_code, indicator="按年度")
            
            if financial_data is not None and not financial_data.empty:
                # 添加证券代码和获取方式字段
                financial_data.insert(0, '证券代码', stock_code)
                financial_data.insert(1, '获取方式', '按年度')
                
                # 添加到当前批次数据中
                batch_data = pd.concat([batch_data, financial_data], ignore_index=True)
                success_count += 1
                
                print(f"✓ 成功获取 {stock_code}({stock_name}) 的财务数据，共{len(financial_data)}条记录")
            else:
                print(f"✗ 警告：{stock_code}({stock_name}) 未获取到数据")
                failed_codes.append(stock_code)
                
        except Exception as e:
            print(f"✗ 获取 {stock_code}({stock_name}) 数据失败：{e}")
            failed_codes.append(stock_code)
        
        processed_count += 1
        
        # 5. 检查是否达到批次大小，如果是则保存数据
        if processed_count % batch_size == 0 and not batch_data.empty:
            print(f"\n正在保存第{processed_count//batch_size}个批次的数据...")
            if save_batch_data(batch_data, output_file, is_first_batch):
                print(f"批次数据保存成功，已处理{processed_count}家公司，成功{success_count}家")
                batch_data = pd.DataFrame()  # 清空批次数据
                is_first_batch = False  # 后续批次不再是第一批次
            else:
                print("批次数据保存失败！")
                break
        
        # 添加延时，避免请求过于频繁
        time.sleep(1)
    
    # 6. 保存最后一个批次的剩余数据
    if not batch_data.empty:
        print(f"\n正在保存最后一个批次的数据...")
        if save_batch_data(batch_data, output_file, is_first_batch):
            print(f"最后批次数据保存成功")
        else:
            print("最后批次数据保存失败！")
    
    # 7. 输出最终统计信息
    print(f"\n数据获取完成！")
    print(f"本次处理的公司数量：{processed_count}")
    print(f"成功获取数据的公司：{success_count}")
    print(f"失败的公司：{len(failed_codes)}")
    print(f"成功率：{success_count/processed_count*100:.2f}%")
    print(f"数据已保存至：{output_file}")
    
    if failed_codes:
        print(f"\n获取失败的证券代码({len(failed_codes)}个)：{failed_codes[:10]}{'...' if len(failed_codes) > 10 else ''}")
        # 保存失败列表到文件
        failed_df = pd.DataFrame({'失败的证券代码': failed_codes})
        failed_df.to_csv("获取失败的证券代码.csv", index=False, encoding='utf-8-sig')
        print("失败列表已保存至：获取失败的证券代码.csv")

def preview_data():
    """
    预览生成的数据文件
    """
    try:
        # 读取时也要指定证券代码为字符串
        data = pd.read_csv("企业关键指标年度.csv", encoding='utf-8-sig', dtype={'证券代码': str})
        print("\n数据预览：")
        print(f"数据形状：{data.shape}")
        print(f"包含的证券代码数量：{data['证券代码'].nunique()}")
        print("\n前5行数据：")
        print(data.head())
        print("\n列名：")
        print(data.columns.tolist())
        
        # 显示证券代码样例以验证格式
        print(f"\n证券代码样例：{data['证券代码'].head(10).tolist()}")
        
    except Exception as e:
        print(f"预览数据失败：{e}")

def check_progress():
    """
    检查当前进度
    """
    try:
        # 读取原始公司列表时指定证券代码为字符串
        company_list = pd.read_csv("沪深上市公司列表.csv", encoding='utf-8', dtype={'证券代码': str})
        total_companies = len(company_list)
        
        # 读取已处理的数据
        if os.path.exists("企业关键指标年度.csv"):
            processed_data = pd.read_csv("企业关键指标年度.csv", encoding='utf-8-sig', dtype={'证券代码': str})
            processed_companies = processed_data['证券代码'].nunique()
            
            print(f"进度统计：")
            print(f"总公司数：{total_companies}")
            print(f"已处理：{processed_companies}")
            print(f"剩余：{total_companies - processed_companies}")
            print(f"完成度：{processed_companies/total_companies*100:.2f}%")
        else:
            print("尚未开始处理数据")
            
    except Exception as e:
        print(f"检查进度失败：{e}")

if __name__ == "__main__":
    print("=" * 50)
    print("沪深上市公司财务数据批量获取工具")
    print("=" * 50)
    
    # 检查当前进度
    check_progress()
    
    print("\n选择操作：")
    print("1. 开始/继续获取数据")
    print("2. 仅预览现有数据")
    print("3. 检查进度")
    
    choice = input("\n请输入选择(1-3)：").strip()
    
    if choice == "1":
        # 询问批次大小
        batch_size_input = input("请输入批次大小(默认10，即每10个公司保存一次)：").strip()
        try:
            batch_size = int(batch_size_input) if batch_size_input else 10
        except:
            batch_size = 10
        
        print(f"开始获取数据，批次大小：{batch_size}")
        get_stock_financial_data(batch_size)
        preview_data()
        
    elif choice == "2":
        preview_data()
        
    elif choice == "3":
        check_progress()
        
    else:
        print("无效选择")
